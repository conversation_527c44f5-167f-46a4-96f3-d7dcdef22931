package com.smartscheduler.config;

import com.smartscheduler.model.*;
import com.smartscheduler.model.Class;
import com.smartscheduler.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.HashSet;
import java.util.Set;

/**
 * Sample Data Initializer for SmartScheduler
 * Tạo dữ liệu mẫu cho hệ thống SmartScheduler
 *
 * Đ<PERSON> kích hoạt: thêm @Profile("dev") hoặc set spring.profiles.active=dev
 */
@Component
@Order(2) // Chạy sau DatabaseInitializer
// @Profile("dev") // Uncomment để chỉ chạy trong môi trường dev
public class SampleDataInitializer implements CommandLineRunner {

    @Autowired private DepartmentRepository departmentRepository;
    @Autowired private MajorRepository majorRepository;
    @Autowired private TeacherRepository teacherRepository;
    @Autowired private CourseRepository courseRepository;
    @Autowired private ClassRepository classRepository;
    @Autowired private CampusRepository campusRepository;
    @Autowired private RoomRepository roomRepository;
    @Autowired private TimeSlotRepository timeSlotRepository;
    @Autowired private WeekDayRepository weekDayRepository;
    @Autowired private AcademicWeekRepository academicWeekRepository;
    @Autowired private UserRepository userRepository;
    @Autowired private RoleRepository roleRepository;
    @Autowired private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        // Chỉ tạo dữ liệu mẫu nếu chưa có
        if (departmentRepository.count() <= 1) { // Chỉ có admin data
            System.out.println("Initializing sample data...");

            initSampleData();

            System.out.println("Sample data initialized successfully!");
        } else {
            System.out.println("Sample data already exists, skipping initialization.");
        }
    }

    private void initSampleData() {
        // 1. Tạo Departments
        createDepartments();

        // 2. Tạo Majors
        createMajors();

        // 3. Tạo Teachers
        createTeachers();

        // 4. Tạo Courses
        createCourses();

        // 5. Tạo Classes
        createClasses();

        // 6. Tạo Rooms (nếu chưa có)
        createRooms();

        // 7. Tạo Academic Weeks
        createAcademicWeeks();

        // 8. Tạo User accounts cho Teachers
        createTeacherUsers();
    }

    private void createDepartments() {
        if (departmentRepository.count() == 0) {
            Department[] departments = {
                Department.builder().name("Khoa Công nghệ Thông tin").code("CNTT")
                    .description("Khoa đào tạo về công nghệ thông tin và khoa học máy tính").build(),
                Department.builder().name("Khoa Kinh tế").code("KT")
                    .description("Khoa đào tạo về kinh tế và quản trị kinh doanh").build(),
                Department.builder().name("Khoa Ngoại ngữ").code("NN")
                    .description("Khoa đào tạo về ngoại ngữ và văn hóa quốc tế").build(),
                Department.builder().name("Khoa Kỹ thuật").code("KTH")
                    .description("Khoa đào tạo về kỹ thuật và công nghệ").build(),
                Department.builder().name("Khoa Khoa học Tự nhiên").code("KHTN")
                    .description("Khoa đào tạo về toán học, vật lý, hóa học").build()
            };

            for (Department dept : departments) {
                departmentRepository.save(dept);
            }
        }
    }

    private void createMajors() {
        if (majorRepository.count() == 0) {
            Department cntt = departmentRepository.findByCode("CNTT").orElse(null);
            Department kt = departmentRepository.findByCode("KT").orElse(null);
            Department nn = departmentRepository.findByCode("NN").orElse(null);
            Department kth = departmentRepository.findByCode("KTH").orElse(null);
            Department khtn = departmentRepository.findByCode("KHTN").orElse(null);

            if (cntt != null) {
                majorRepository.save(Major.builder().name("Công nghệ Phần mềm").code("CNPM")
                    .description("Ngành đào tạo về phát triển phần mềm").department(cntt).build());
                majorRepository.save(Major.builder().name("Hệ thống Thông tin").code("HTTT")
                    .description("Ngành đào tạo về quản lý hệ thống thông tin").department(cntt).build());
                majorRepository.save(Major.builder().name("An toàn Thông tin").code("ATTT")
                    .description("Ngành đào tạo về bảo mật và an toàn thông tin").department(cntt).build());
            }

            if (kt != null) {
                majorRepository.save(Major.builder().name("Quản trị Kinh doanh").code("QTKD")
                    .description("Ngành đào tạo về quản lý và điều hành doanh nghiệp").department(kt).build());
                majorRepository.save(Major.builder().name("Kế toán").code("KT")
                    .description("Ngành đào tạo về kế toán và tài chính").department(kt).build());
            }

            if (nn != null) {
                majorRepository.save(Major.builder().name("Tiếng Anh").code("TA")
                    .description("Ngành đào tạo về ngôn ngữ và văn hóa Anh-Mỹ").department(nn).build());
                majorRepository.save(Major.builder().name("Tiếng Nhật").code("TN")
                    .description("Ngành đào tạo về ngôn ngữ và văn hóa Nhật Bản").department(nn).build());
            }

            if (kth != null) {
                majorRepository.save(Major.builder().name("Kỹ thuật Điện").code("KTD")
                    .description("Ngành đào tạo về kỹ thuật điện và điện tử").department(kth).build());
                majorRepository.save(Major.builder().name("Kỹ thuật Cơ khí").code("KTCK")
                    .description("Ngành đào tạo về kỹ thuật cơ khí và chế tạo máy").department(kth).build());
            }

            if (khtn != null) {
                majorRepository.save(Major.builder().name("Toán học").code("TH")
                    .description("Ngành đào tạo về toán học ứng dụng").department(khtn).build());
            }
        }
    }

    private void createTeachers() {
        if (teacherRepository.count() == 0) {
            Department cntt = departmentRepository.findByCode("CNTT").orElse(null);
            Department kt = departmentRepository.findByCode("KT").orElse(null);
            Department nn = departmentRepository.findByCode("NN").orElse(null);
            Department kth = departmentRepository.findByCode("KTH").orElse(null);
            Department khtn = departmentRepository.findByCode("KHTN").orElse(null);

            Teacher[] teachers = {
                Teacher.builder().firstName("Nguyễn").lastName("Văn An").teacherCode("GV001")
                    .email("<EMAIL>").phone("0901234567").department(cntt).build(),
                Teacher.builder().firstName("Trần").lastName("Thị Bình").teacherCode("GV002")
                    .email("<EMAIL>").phone("0901234568").department(cntt).build(),
                Teacher.builder().firstName("Lê").lastName("Hoàng Cường").teacherCode("GV003")
                    .email("<EMAIL>").phone("0901234569").department(cntt).build(),
                Teacher.builder().firstName("Phạm").lastName("Thị Dung").teacherCode("GV004")
                    .email("<EMAIL>").phone("0901234570").department(kt).build(),
                Teacher.builder().firstName("Hoàng").lastName("Văn Em").teacherCode("GV005")
                    .email("<EMAIL>").phone("0901234571").department(kt).build(),
                Teacher.builder().firstName("Vũ").lastName("Thị Phương").teacherCode("GV006")
                    .email("<EMAIL>").phone("0901234572").department(nn).build(),
                Teacher.builder().firstName("Đặng").lastName("Minh Giang").teacherCode("GV007")
                    .email("<EMAIL>").phone("0901234573").department(nn).build(),
                Teacher.builder().firstName("Bùi").lastName("Văn Hải").teacherCode("GV008")
                    .email("<EMAIL>").phone("0901234574").department(kth).build(),
                Teacher.builder().firstName("Ngô").lastName("Thị Lan").teacherCode("GV009")
                    .email("<EMAIL>").phone("0901234575").department(kth).build(),
                Teacher.builder().firstName("Đinh").lastName("Văn Khoa").teacherCode("GV010")
                    .email("<EMAIL>").phone("0901234576").department(khtn).build()
            };

            for (Teacher teacher : teachers) {
                if (teacher.getDepartment() != null) {
                    teacherRepository.save(teacher);
                }
            }
        }
    }

    private void createCourses() {
        if (courseRepository.count() == 0) {
            Course[] courses = {
                // IT Courses
                Course.builder().name("Lập trình Java").code("JAVA101")
                    .description("Môn học cơ bản về lập trình Java").credits(3).totalHours(45)
                    .teachingFormat(Course.TeachingFormat.THEORY).build(),
                Course.builder().name("Thực hành Java").code("JAVA101P")
                    .description("Thực hành lập trình Java").credits(2).totalHours(30)
                    .teachingFormat(Course.TeachingFormat.PRACTICE).build(),
                Course.builder().name("Cơ sở dữ liệu").code("DB101")
                    .description("Môn học về thiết kế và quản lý cơ sở dữ liệu").credits(3).totalHours(45)
                    .teachingFormat(Course.TeachingFormat.THEORY).build(),
                Course.builder().name("Kỹ thuật phần mềm").code("SE101")
                    .description("Môn học về quy trình phát triển phần mềm").credits(3).totalHours(45)
                    .teachingFormat(Course.TeachingFormat.THEORY).build(),

                // Business Courses
                Course.builder().name("Quản trị học").code("MGT101")
                    .description("Môn học cơ bản về quản trị doanh nghiệp").credits(3).totalHours(45)
                    .teachingFormat(Course.TeachingFormat.THEORY).build(),
                Course.builder().name("Kế toán tài chính").code("ACC101")
                    .description("Môn học về kế toán và tài chính doanh nghiệp").credits(3).totalHours(45)
                    .teachingFormat(Course.TeachingFormat.THEORY).build(),

                // Language Courses
                Course.builder().name("Tiếng Anh giao tiếp").code("ENG101")
                    .description("Môn học về kỹ năng giao tiếp tiếng Anh").credits(2).totalHours(30)
                    .teachingFormat(Course.TeachingFormat.PRACTICE).build(),
                Course.builder().name("Tiếng Nhật cơ bản").code("JPN101")
                    .description("Môn học tiếng Nhật cho người mới bắt đầu").credits(3).totalHours(45)
                    .teachingFormat(Course.TeachingFormat.THEORY).build(),

                // Engineering Courses
                Course.builder().name("Mạch điện tử").code("ELE101")
                    .description("Môn học về thiết kế mạch điện tử").credits(3).totalHours(45)
                    .teachingFormat(Course.TeachingFormat.THEORY).build(),
                Course.builder().name("Cơ học kỹ thuật").code("MECH101")
                    .description("Môn học về cơ học ứng dụng trong kỹ thuật").credits(3).totalHours(45)
                    .teachingFormat(Course.TeachingFormat.THEORY).build(),

                // Math Courses
                Course.builder().name("Giải tích 1").code("MATH101")
                    .description("Môn học về giải tích toán học").credits(4).totalHours(60)
                    .teachingFormat(Course.TeachingFormat.THEORY).build(),
                Course.builder().name("Đại số tuyến tính").code("MATH102")
                    .description("Môn học về đại số và ma trận").credits(3).totalHours(45)
                    .teachingFormat(Course.TeachingFormat.THEORY).build()
            };

            for (Course course : courses) {
                courseRepository.save(course);
            }
        }
    }

    private void createClasses() {
        if (classRepository.count() == 0) {
            Major cnpm = majorRepository.findByCode("CNPM").orElse(null);
            Major httt = majorRepository.findByCode("HTTT").orElse(null);
            Major attt = majorRepository.findByCode("ATTT").orElse(null);
            Major qtkd = majorRepository.findByCode("QTKD").orElse(null);

            if (cnpm != null) {
                classRepository.save(Class.builder().name("CNPM2024A").code("CNPM24A")
                    .year(2024).semester(1).studentCount(35).major(cnpm).build());
                classRepository.save(Class.builder().name("CNPM2024B").code("CNPM24B")
                    .year(2024).semester(1).studentCount(32).major(cnpm).build());
            }

            if (httt != null) {
                classRepository.save(Class.builder().name("HTTT2024A").code("HTTT24A")
                    .year(2024).semester(1).studentCount(30).major(httt).build());
            }

            if (attt != null) {
                classRepository.save(Class.builder().name("ATTT2024A").code("ATTT24A")
                    .year(2024).semester(1).studentCount(28).major(attt).build());
            }

            if (qtkd != null) {
                classRepository.save(Class.builder().name("QTKD2024A").code("QTKD24A")
                    .year(2024).semester(1).studentCount(40).major(qtkd).build());
                classRepository.save(Class.builder().name("QTKD2024B").code("QTKD24B")
                    .year(2024).semester(1).studentCount(38).major(qtkd).build());
            }
        }
    }

    private void createRooms() {
        if (roomRepository.count() == 0) {
            Campus campus1 = campusRepository.findByCode("CS1").orElse(null);
            Campus campus2 = campusRepository.findByCode("CS2").orElse(null);

            if (campus1 != null) {
                // Theory rooms
                roomRepository.save(Room.builder().name("Phòng A101").code("A101")
                    .capacity(50).roomType(Room.RoomType.THEORY).campus(campus1).build());
                roomRepository.save(Room.builder().name("Phòng A102").code("A102")
                    .capacity(45).roomType(Room.RoomType.THEORY).campus(campus1).build());

                // Practice rooms
                roomRepository.save(Room.builder().name("Phòng B101").code("B101")
                    .capacity(30).roomType(Room.RoomType.PRACTICE).campus(campus1).build());
                roomRepository.save(Room.builder().name("Phòng B102").code("B102")
                    .capacity(30).roomType(Room.RoomType.PRACTICE).campus(campus1).build());
            }

            if (campus2 != null) {
                roomRepository.save(Room.builder().name("Phòng C101").code("C101")
                    .capacity(60).roomType(Room.RoomType.THEORY).campus(campus2).build());
                roomRepository.save(Room.builder().name("Phòng D101").code("D101")
                    .capacity(35).roomType(Room.RoomType.PRACTICE).campus(campus2).build());
            }
        }
    }

    private void createAcademicWeeks() {
        if (academicWeekRepository.count() <= 5) { // Chỉ có dữ liệu cơ bản
            LocalDate startDate = LocalDate.of(2024, 9, 2);

            for (int week = 6; week <= 15; week++) {
                LocalDate weekStart = startDate.plusWeeks(week - 1);
                LocalDate weekEnd = weekStart.plusDays(6);

                academicWeekRepository.save(AcademicWeek.builder()
                    .weekNumber(week)
                    .startDate(weekStart)
                    .endDate(weekEnd)
                    .academicYear("2024-2025")
                    .semester(1)
                    .build());
            }
        }
    }

    private void createTeacherUsers() {
        Role teacherRole = roleRepository.findByName("ROLE_TEACHER").orElse(null);
        if (teacherRole == null) return;

        // Tạo user cho 5 giảng viên đầu tiên
        for (int i = 1; i <= 5; i++) {
            String username = "gv" + String.format("%03d", i);
            if (!userRepository.existsByUsername(username)) {
                Teacher teacher = teacherRepository.findByTeacherCode("GV" + String.format("%03d", i)).orElse(null);
                if (teacher != null) {
                    Set<Role> roles = new HashSet<>();
                    roles.add(teacherRole);

                    User user = User.builder()
                        .username(username)
                        .password(passwordEncoder.encode("password123"))
                        .email(teacher.getEmail())
                        .teacher(teacher) // Set teacher reference - User có teacher_id foreign key
                        .isAccountNonExpired(true)
                        .isAccountNonLocked(true)
                        .isCredentialsNonExpired(true)
                        .isEnabled(true)
                        .roles(roles)
                        .build();

                    userRepository.save(user);

                    // Cập nhật teacher với user reference (bidirectional relationship)
                    teacher.setUser(user);
                    teacherRepository.save(teacher);
                }
            }
        }
    }
}
